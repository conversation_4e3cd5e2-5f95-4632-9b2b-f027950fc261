<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 20px;
        }
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-card p {
            font-size: 1.1rem;
            margin: 0;
        }
        .stat-card.points {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.images {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.videos {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.spent {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .history-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .history-item.image {
            border-left-color: #28a745;
        }
        .history-item.video {
            border-left-color: #dc3545;
        }
        .transaction-item {
            border-left: 4px solid #6c757d;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .transaction-item.income {
            border-left-color: #28a745;
        }
        .transaction-item.expense {
            border-left-color: #dc3545;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .unread-messages-alert {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }
        .unread-messages-alert .alert-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        .unread-messages-alert .alert-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .unread-messages-alert .alert-text {
            flex-grow: 1;
        }
        .unread-messages-alert .alert-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .unread-messages-alert .alert-description {
            font-size: 1rem;
            opacity: 0.9;
        }
        .unread-messages-alert .btn-check-messages {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .unread-messages-alert .btn-check-messages:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 8px 35px rgba(255, 107, 107, 0.5);
            }
            100% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
        }

        .stat-card.shares {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .stat-card.views {
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }

        .share-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .share-item:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .share-item.expired {
            opacity: 0.6;
            background: #f1f1f1;
        }

        .share-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .share-meta {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .share-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .share-url {
            font-family: monospace;
            font-size: 0.8rem;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            word-break: break-all;
        }

        /* API文档折叠样式 */
        .api-documentation-toggle {
            transition: all 0.3s ease;
        }

        .api-documentation-toggle:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .api-documentation-content {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="btn btn-primary back-btn">
        <i class="fas fa-arrow-left me-2"></i>返回主页
    </a>

    <div class="container">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1><i class="fas fa-user-circle me-3"></i>个人中心</h1>
                <p class="text-muted">欢迎，{{ user_info.username }}！</p>
            </div>
        </div>

        <!-- 未读消息通知 -->
        {% if user_info.unread_messages_count > 0 %}
        <div class="row">
            <div class="col-12">
                <div class="unread-messages-alert">
                    <div class="alert-content">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope alert-icon"></i>
                            <div class="alert-text">
                                <div class="alert-title">您有 {{ user_info.unread_messages_count }} 条未读站内信</div>
                                <div class="alert-description">点击查看重要消息和通知</div>
                            </div>
                        </div>
                        <a href="/messages" class="btn-check-messages">
                            <i class="fas fa-eye me-2"></i>查看消息
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-md-2 col-sm-6">
                <div class="stat-card points">
                    <h3>{{ user_info.points }}</h3>
                    <p><i class="fas fa-coins me-2"></i>当前积分</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card spent">
                    <h3>{{ user_info.total_spent_points }}</h3>
                    <p><i class="fas fa-credit-card me-2"></i>总花费积分</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card images">
                    <h3>{{ user_info.total_images }}</h3>
                    <p><i class="fas fa-image me-2"></i>生成图片</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card videos">
                    <h3>{{ user_info.total_videos }}</h3>
                    <p><i class="fas fa-video me-2"></i>生成视频</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card shares">
                    <h3 id="totalShares">-</h3>
                    <p><i class="fas fa-share-alt me-2"></i>分享链接</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card views">
                    <h3 id="totalViews">-</h3>
                    <p><i class="fas fa-eye me-2"></i>分享浏览</p>
                </div>
            </div>
        </div>

        <!-- 分享管理 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-share-alt me-2"></i>我的分享</h5>
                        <button class="btn btn-primary btn-sm" onclick="loadMyShares()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="sharesContainer">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户信息 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-4"><strong>用户名：</strong></div>
                            <div class="col-8">{{ user_info.username }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>注册时间：</strong></div>
                            <div class="col-8">{{ user_info.created_at[:19] if user_info.created_at else '未知' }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>最后登录：</strong></div>
                            <div class="col-8">{{ user_info.last_login[:19] if user_info.last_login else '未知' }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>总生成次数：</strong></div>
                            <div class="col-8">{{ user_info.total_generated }} 次</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>生成统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6"><strong>图片生成：</strong></div>
                            <div class="col-6">{{ user_info.total_images }} 张</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>视频生成：</strong></div>
                            <div class="col-6">{{ user_info.total_videos }} 个</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>成功率：</strong></div>
                            <div class="col-6">
                                {% if user_info.total_generated > 0 %}
                                    {{ "%.1f"|format((user_info.total_images + user_info.total_videos) / user_info.total_generated * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>平均每次消耗：</strong></div>
                            <div class="col-6">
                                {% if user_info.total_generated > 0 %}
                                    {{ "%.1f"|format(user_info.total_spent_points / user_info.total_generated) }} 积分
                                {% else %}
                                    0 积分
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近交易记录 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exchange-alt me-2"></i>最近交易记录</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if user_info.recent_transactions %}
                            {% for transaction in user_info.recent_transactions %}
                            <div class="transaction-item {{ 'income' if transaction.points_change > 0 else 'expense' }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            {% if transaction.points_change > 0 %}
                                                <span class="text-success">+{{ transaction.points_change }}</span>
                                            {% else %}
                                                <span class="text-danger">{{ transaction.points_change }}</span>
                                            {% endif %}
                                        </strong>
                                        <span class="ms-2">积分</span>
                                    </div>
                                    <small class="text-muted">{{ transaction.timestamp[:19] }}</small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">{{ transaction.description }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">暂无交易记录</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>最近生成记录</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if user_info.recent_history %}
                            {% for record in user_info.recent_history %}
                            <div class="history-item {{ record.type }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            {% if record.type == 'image' %}
                                                <i class="fas fa-image text-success me-1"></i>图片生成
                                            {% else %}
                                                <i class="fas fa-video text-danger me-1"></i>视频生成
                                            {% endif %}
                                        </strong>
                                        {% if record.success %}
                                            <span class="badge bg-success ms-2">成功</span>
                                        {% else %}
                                            <span class="badge bg-danger ms-2">失败</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">{{ record.timestamp[:19] }}</small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">{{ record.prompt[:100] }}{% if record.prompt|length > 100 %}...{% endif %}</small>
                                </div>
                                {% if record.model_name %}
                                <div class="mt-1">
                                    <small class="text-info">模型：{{ record.model_name }}</small>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">暂无生成记录</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- API密钥管理 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-key me-2"></i>API密钥管理</h5>
                    </div>
                    <div class="card-body">
                        {% if user_info.api_key_info %}
                        <div class="alert alert-success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><i class="fas fa-check-circle me-2"></i>API密钥已生成</h6>
                                    <small class="text-muted">
                                        创建时间: {{ user_info.api_key_info.created_at[:19] if user_info.api_key_info.created_at else '未知' }}
                                        {% if user_info.api_key_info.last_used %}
                                        | 最后使用: {{ user_info.api_key_info.last_used[:19] }}
                                        {% endif %}
                                        | 使用次数: {{ user_info.api_key_info.usage_count or 0 }}
                                    </small>
                                </div>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="showApiKey()">
                                        <i class="fas fa-eye me-1"></i>查看密钥
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm me-2" onclick="regenerateApiKey()">
                                        <i class="fas fa-sync me-1"></i>重新生成
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteApiKey()">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <h6 class="mb-1"><i class="fas fa-info-circle me-2"></i>您还没有API密钥</h6>
                            <p class="mb-2">生成API密钥后，您可以通过API接口调用画图功能</p>
                            <button class="btn btn-primary" onclick="generateApiKey()">
                                <i class="fas fa-plus me-1"></i>生成API密钥
                            </button>
                        </div>
                        {% endif %}

                        <!-- API使用说明 -->
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-book me-2"></i>API使用说明</h6>
                                <button class="btn btn-outline-primary btn-sm api-documentation-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#apiDocumentation" aria-expanded="false" aria-controls="apiDocumentation">
                                    <i class="fas fa-chevron-down me-1"></i>展开查看
                                </button>
                            </div>
                            <div class="collapse mt-2" id="apiDocumentation">
                                <div class="bg-light p-3 rounded">
                                <div class="alert alert-info mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>重要提示：</strong>请妥善保管您的API密钥，不要在客户端代码中暴露。建议在服务器端使用API。
                                </div>

                                <h6 class="text-primary mb-2"><i class="fas fa-link me-1"></i>接口信息</h6>
                                <div class="mb-3">
                                    <p class="mb-1"><strong>接口地址:</strong></p>
                                    <div class="input-group">
                                        <input type="text" class="form-control font-monospace" id="apiEndpoint" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyApiEndpoint()">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">POST请求</small>
                                </div>

                                <h6 class="text-primary mb-2"><i class="fas fa-key me-1"></i>认证方式</h6>
                                <div class="mb-3">
                                    <p class="mb-1"><strong>请求头:</strong></p>
                                    <code class="d-block bg-white p-2 border rounded">Authorization: Bearer YOUR_API_KEY</code>
                                    <code class="d-block bg-white p-2 border rounded mt-1">Content-Type: application/json</code>
                                </div>

                                <h6 class="text-primary mb-2"><i class="fas fa-cogs me-1"></i>请求参数</h6>
                                <div class="table-responsive mb-3">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>参数名</th>
                                                <th>类型</th>
                                                <th>必需</th>
                                                <th>默认值</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>prompt</code></td>
                                                <td>string</td>
                                                <td><span class="badge bg-danger">是</span></td>
                                                <td>-</td>
                                                <td>图像生成提示词</td>
                                            </tr>
                                            <tr>
                                                <td><code>negative_prompt</code></td>
                                                <td>string</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>""</td>
                                                <td>负面提示词，避免不想要的内容</td>
                                            </tr>
                                            <tr>
                                                <td><code>width</code></td>
                                                <td>integer</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>512</td>
                                                <td>图像宽度 (64-2048)</td>
                                            </tr>
                                            <tr>
                                                <td><code>height</code></td>
                                                <td>integer</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>512</td>
                                                <td>图像高度 (64-2048)</td>
                                            </tr>
                                            <tr>
                                                <td><code>steps</code></td>
                                                <td>integer</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>20</td>
                                                <td>生成步数 (1-100)</td>
                                            </tr>
                                            <tr>
                                                <td><code>cfg</code></td>
                                                <td>float</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>7.0</td>
                                                <td>CFG引导强度 (1-30)</td>
                                            </tr>
                                            <tr>
                                                <td><code>model_index</code></td>
                                                <td>integer</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>0</td>
                                                <td>模型索引，参考主页模型列表</td>
                                            </tr>
                                            <tr>
                                                <td><code>seed</code></td>
                                                <td>integer</td>
                                                <td><span class="badge bg-secondary">否</span></td>
                                                <td>-1</td>
                                                <td>随机种子，-1为随机</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h6 class="text-primary mb-2"><i class="fas fa-check-circle me-1"></i>响应格式</h6>
                                <div class="mb-3">
                                    <p class="mb-1"><strong>成功响应 (200):</strong></p>
                                    <pre class="bg-white p-2 border rounded small"><code>{
  "success": true,
  "message": "图像生成成功",
  "data": {
    "image_url": "https://example.com/image.jpg",
    "image_id": "12345",
    "model_name": "模型名称",
    "points_used": 1,
    "remaining_points": 99
  }
}</code></pre>

                                    <p class="mb-1 mt-2"><strong>错误响应 (400/401/500):</strong></p>
                                    <pre class="bg-white p-2 border rounded small"><code>{
  "error": "错误描述信息"
}</code></pre>
                                </div>

                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>积分消耗：</strong>每次成功的API调用将消耗 {{ user_info.generation_cost or 1 }} 积分。生成失败不扣除积分。
                                </div>

                                <!-- 代码示例 -->
                                <div class="mt-4">
                                    <h6 class="text-primary mb-2"><i class="fas fa-code me-1"></i>代码示例</h6>

                                    <!-- 选项卡导航 -->
                                    <ul class="nav nav-tabs" id="codeExampleTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button" role="tab">cURL</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button" role="tab">Python</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="javascript-tab" data-bs-toggle="tab" data-bs-target="#javascript" type="button" role="tab">JavaScript</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="nodejs-tab" data-bs-toggle="tab" data-bs-target="#nodejs" type="button" role="tab">Node.js</button>
                                        </li>
                                    </ul>

                                    <!-- 选项卡内容 -->
                                    <div class="tab-content" id="codeExampleTabsContent">
                                        <!-- cURL 示例 -->
                                        <div class="tab-pane fade show active" id="curl" role="tabpanel">
                                            <div class="position-relative">
                                                <pre class="bg-dark text-light p-3 rounded-bottom"><code>curl -X POST "<span id="curlEndpoint"></span>" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful anime girl, detailed face, high quality",
    "negative_prompt": "blurry, low quality, distorted",
    "width": 512,
    "height": 512,
    "steps": 20,
    "cfg": 7.0,
    "model_index": 0,
    "seed": -1
  }'</code></pre>
                                                <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" onclick="copyCode('curl')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Python 示例 -->
                                        <div class="tab-pane fade" id="python" role="tabpanel">
                                            <div class="position-relative">
                                                <pre class="bg-dark text-light p-3 rounded-bottom"><code>import requests
import json

API_KEY = "YOUR_API_KEY"
BASE_URL = "<span id="pythonBaseUrl"></span>"

def generate_image(prompt, negative_prompt="", width=512, height=512):
    url = f"{BASE_URL}/api/v1/generate_image"

    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": width,
        "height": height,
        "steps": 20,
        "cfg": 7.0,
        "model_index": 0,
        "seed": -1
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print(f"生成成功！图像URL: {result['data']['image_url']}")
            return result['data']['image_url']
        else:
            print(f"生成失败: {result.get('error', '未知错误')}")
    else:
        print(f"请求失败: {response.status_code}")

    return None

# 使用示例
image_url = generate_image(
    prompt="a beautiful anime girl, detailed face, high quality",
    negative_prompt="blurry, low quality, distorted"
)</code></pre>
                                                <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" onclick="copyCode('python')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- JavaScript 示例 -->
                                        <div class="tab-pane fade" id="javascript" role="tabpanel">
                                            <div class="position-relative">
                                                <pre class="bg-dark text-light p-3 rounded-bottom"><code>const API_KEY = "YOUR_API_KEY";
const BASE_URL = "<span id="jsBaseUrl"></span>";

async function generateImage(prompt, negativePrompt = "", width = 512, height = 512) {
    const url = `${BASE_URL}/api/v1/generate_image`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: prompt,
                negative_prompt: negativePrompt,
                width: width,
                height: height,
                steps: 20,
                cfg: 7.0,
                model_index: 0,
                seed: -1
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            console.log('生成成功！图像URL:', result.data.image_url);
            return result.data.image_url;
        } else {
            console.error('生成失败:', result.error || '未知错误');
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return null;
    }
}

// 使用示例
generateImage(
    "a beautiful anime girl, detailed face, high quality",
    "blurry, low quality, distorted"
).then(imageUrl => {
    if (imageUrl) {
        console.log('图像生成完成:', imageUrl);
    }
});</code></pre>
                                                <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" onclick="copyCode('javascript')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Node.js 示例 -->
                                        <div class="tab-pane fade" id="nodejs" role="tabpanel">
                                            <div class="position-relative">
                                                <pre class="bg-dark text-light p-3 rounded-bottom"><code>const axios = require('axios');

const API_KEY = "YOUR_API_KEY";
const BASE_URL = "<span id="nodejsBaseUrl"></span>";

async function generateImage(prompt, negativePrompt = "", width = 512, height = 512) {
    const url = `${BASE_URL}/api/v1/generate_image`;

    try {
        const response = await axios.post(url, {
            prompt: prompt,
            negative_prompt: negativePrompt,
            width: width,
            height: height,
            steps: 20,
            cfg: 7.0,
            model_index: 0,
            seed: -1
        }, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.success) {
            console.log('生成成功！图像URL:', response.data.data.image_url);
            return response.data.data.image_url;
        } else {
            console.error('生成失败:', response.data.error || '未知错误');
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error.response?.data || error.message);
        return null;
    }
}

// 使用示例
(async () => {
    const imageUrl = await generateImage(
        "a beautiful anime girl, detailed face, high quality",
        "blurry, low quality, distorted"
    );

    if (imageUrl) {
        console.log('图像生成完成:', imageUrl);
    }
})();</code></pre>
                                                <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" onclick="copyCode('nodejs')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 快速测试工具 -->
                                <div class="mt-4">
                                    <h6 class="text-primary mb-2"><i class="fas fa-flask me-1"></i>API测试工具</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">提示词</label>
                                                        <textarea class="form-control" id="testPrompt" rows="3" placeholder="输入图像描述...">a beautiful anime girl, detailed face, high quality</textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">负面提示词</label>
                                                        <textarea class="form-control" id="testNegativePrompt" rows="2" placeholder="输入不想要的内容...">blurry, low quality, distorted</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">宽度</label>
                                                                <input type="number" class="form-control" id="testWidth" value="512" min="64" max="2048">
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">高度</label>
                                                                <input type="number" class="form-control" id="testHeight" value="512" min="64" max="2048">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">步数</label>
                                                                <input type="number" class="form-control" id="testSteps" value="20" min="1" max="100">
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">CFG</label>
                                                                <input type="number" class="form-control" id="testCfg" value="7" min="1" max="30" step="0.1">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <button class="btn btn-primary" onclick="testApiCall()" id="testApiBtn">
                                                    <i class="fas fa-play me-1"></i>测试API调用
                                                </button>
                                                <button class="btn btn-secondary ms-2" onclick="generateTestCode()">
                                                    <i class="fas fa-code me-1"></i>生成代码
                                                </button>
                                            </div>
                                            <div id="testResult" class="mt-3" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>快捷操作</h5>
                    </div>
                    <div class="card-body text-center">
                        <a href="/" class="btn btn-primary me-2">
                            <i class="fas fa-paint-brush me-1"></i>开始创作
                        </a>
                        <a href="/redeem" class="btn btn-success me-2">
                            <i class="fas fa-gift me-1"></i>兑换积分
                        </a>
                        <button id="checkinBtn" class="btn btn-info me-2">
                            <i class="fas fa-calendar-check me-1"></i>每日签到
                        </button>
                        <a href="/imagelist" class="btn btn-warning me-2">
                            <i class="fas fa-images me-1"></i>我的画廊
                        </a>
                        <a href="/apply" class="btn btn-secondary">
                            <i class="fas fa-paper-plane me-1"></i>申请点数
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化API端点显示
            function initApiEndpoint() {
                const apiEndpointInput = document.getElementById('apiEndpoint');
                if (apiEndpointInput) {
                    // 获取当前域名，如果是localhost或127.0.0.1则使用配置的域名
                    let baseUrl = window.location.origin;
                    if (baseUrl.includes('127.0.0.1') || baseUrl.includes('localhost')) {
                        // 使用配置的域名或默认域名
                        baseUrl = '{{ site_domain or "https://sd.exacg.cc" }}';
                    }
                    apiEndpointInput.value = `${baseUrl}/api/v1/generate_image`;

                    // 更新代码示例中的URL
                    updateCodeExamples(baseUrl);
                }
            }

            // 更新代码示例中的URL
            function updateCodeExamples(baseUrl) {
                const curlEndpoint = document.getElementById('curlEndpoint');
                const pythonBaseUrl = document.getElementById('pythonBaseUrl');
                const jsBaseUrl = document.getElementById('jsBaseUrl');
                const nodejsBaseUrl = document.getElementById('nodejsBaseUrl');

                if (curlEndpoint) curlEndpoint.textContent = `${baseUrl}/api/v1/generate_image`;
                if (pythonBaseUrl) pythonBaseUrl.textContent = baseUrl;
                if (jsBaseUrl) jsBaseUrl.textContent = baseUrl;
                if (nodejsBaseUrl) nodejsBaseUrl.textContent = baseUrl;
            }

            // 显示消息提示
            function showMessage(message, type = 'info') {
                const container = document.getElementById('messageContainer');
                const alertClass = type === 'success' ? 'alert-success' :
                                 type === 'error' ? 'alert-danger' : 'alert-info';

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                container.appendChild(alertDiv);

                // 3秒后自动消失
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            }

            // 初始化API端点
            initApiEndpoint();
            // 检查签到状态
            function checkCheckinStatus() {
                const checkinBtn = document.getElementById('checkinBtn');
                if (!checkinBtn) return;

                fetch('/checkin_status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.has_checked_in) {
                        // 今日已签到，更新按钮状态
                        checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                        checkinBtn.classList.remove('btn-info');
                        checkinBtn.classList.add('btn-success');
                        checkinBtn.disabled = true;
                    }
                })
                .catch(error => {
                    console.error('检查签到状态错误:', error);
                });
            }

            // 每日签到功能
            const checkinBtn = document.getElementById('checkinBtn');
            if (checkinBtn) {
                checkinBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 禁用按钮防止重复点击
                    checkinBtn.disabled = true;
                    const originalText = checkinBtn.innerHTML;
                    checkinBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>签到中...';

                    fetch('/daily_checkin', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新页面上的积分显示
                            const currentPointsElements = document.querySelectorAll('.stat-card.points h3');
                            if (currentPointsElements.length > 0) {
                                currentPointsElements[0].textContent = data.new_points;
                            }

                            // 显示成功消息
                            showMessage(`${data.message} 当前积分：${data.new_points}`, 'success');

                            // 签到成功后更新按钮状态
                            checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                            checkinBtn.classList.remove('btn-info');
                            checkinBtn.classList.add('btn-success');
                        } else {
                            showMessage(data.message, 'error');
                            // 恢复按钮状态
                            checkinBtn.innerHTML = originalText;
                            checkinBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('签到错误:', error);
                        showMessage('签到失败，请重试', 'error');
                        // 恢复按钮状态
                        checkinBtn.innerHTML = originalText;
                        checkinBtn.disabled = false;
                    });
                });
            }

            // 页面加载时检查签到状态
            checkCheckinStatus();
            loadMyShares();

            // 初始化API文档折叠功能
            initApiDocumentationCollapse();
        });

        // 初始化API文档折叠功能
        function initApiDocumentationCollapse() {
            const collapseElement = document.getElementById('apiDocumentation');
            const toggleButton = document.querySelector('[data-bs-target="#apiDocumentation"]');

            if (collapseElement && toggleButton) {
                collapseElement.addEventListener('show.bs.collapse', function () {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-up me-1"></i>收起';
                });

                collapseElement.addEventListener('hide.bs.collapse', function () {
                    toggleButton.innerHTML = '<i class="fas fa-chevron-down me-1"></i>展开查看';
                });
            }
        }

        // 分享管理功能
        async function loadMyShares() {
            const sharesContainer = document.getElementById('sharesContainer');
            const totalSharesElement = document.getElementById('totalShares');
            const totalViewsElement = document.getElementById('totalViews');

            try {
                sharesContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>加载中...</div>';

                const response = await fetch('/api/my_shares');
                const data = await response.json();

                if (data.success) {
                    const shares = data.shares;

                    // 更新统计数据
                    const activeShares = shares.filter(share => !share.is_expired);
                    const totalViews = shares.reduce((sum, share) => sum + share.view_count, 0);

                    totalSharesElement.textContent = activeShares.length;
                    totalViewsElement.textContent = totalViews;

                    if (shares.length === 0) {
                        sharesContainer.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-share-alt fa-2x mb-3"></i>
                                <p>您还没有创建任何分享链接</p>
                                <p><small>前往<a href="/imagelist">画廊</a>选择图片创建分享链接</small></p>
                            </div>
                        `;
                    } else {
                        let html = '';
                        shares.forEach(share => {
                            const isExpired = share.is_expired;
                            const statusBadge = isExpired ?
                                '<span class="badge bg-secondary">已过期</span>' :
                                '<span class="badge bg-success">有效</span>';

                            const keyInfo = `<span class="text-warning"><i class="fas fa-key me-1"></i>需要秘钥</span>`;

                            html += `
                                <div class="share-item ${isExpired ? 'expired' : ''}">
                                    <div class="share-title">
                                        ${share.title} ${statusBadge}
                                    </div>
                                    <div class="share-meta">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <i class="fas fa-images me-1"></i>${share.images.length} 张图片
                                                <span class="ms-3"><i class="fas fa-eye me-1"></i>${share.view_count} 次浏览</span>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <i class="fas fa-calendar me-1"></i>${share.created_at.substring(0, 19).replace('T', ' ')}
                                                <span class="ms-3">${keyInfo}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="share-url">
                                        ${share.share_url}
                                    </div>
                                    <div class="share-actions">
                                        <button class="btn btn-outline-primary btn-sm" onclick="copyShareUrl('${share.share_url}')">
                                            <i class="fas fa-copy me-1"></i>复制链接
                                        </button>
                                        <a href="${share.share_url}" target="_blank" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-external-link-alt me-1"></i>访问
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteShare('${share.id}', '${share.title}')">
                                            <i class="fas fa-trash me-1"></i>删除
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        sharesContainer.innerHTML = html;
                    }
                } else {
                    sharesContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载分享列表失败：${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载分享列表失败:', error);
                sharesContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        网络错误，请稍后重试
                    </div>
                `;
            }
        }

        function copyShareUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                // 显示复制成功提示
                const btn = event.target.closest('button');
                const originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-success');

                setTimeout(() => {
                    btn.innerHTML = originalHtml;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-primary');
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制链接');
            });
        }

        async function deleteShare(shareId, shareTitle) {
            if (!confirm(`确定要删除分享"${shareTitle}"吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch('/api/delete_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        share_id: shareId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('分享链接已删除');
                    loadMyShares(); // 重新加载列表
                } else {
                    alert('删除失败：' + data.message);
                }
            } catch (error) {
                console.error('删除分享失败:', error);
                alert('删除失败，请稍后重试');
            }
        }

        // 复制API端点
        function copyApiEndpoint() {
            const apiEndpointInput = document.getElementById('apiEndpoint');
            if (apiEndpointInput) {
                apiEndpointInput.select();
                document.execCommand('copy');

                // 显示复制成功提示
                const btn = event.target.closest('button');
                const originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.classList.remove('btn-outline-secondary');
                btn.classList.add('btn-success');

                setTimeout(() => {
                    btn.innerHTML = originalHtml;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-secondary');
                }, 2000);
            }
        }

        // 复制代码示例
        function copyCode(type) {
            const codeElement = document.querySelector(`#${type} code`);
            if (codeElement) {
                const text = codeElement.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    // 显示复制成功提示
                    const btn = event.target.closest('button');
                    const originalHtml = btn.innerHTML;
                    btn.innerHTML = '<i class="fas fa-check"></i>';
                    btn.classList.remove('btn-outline-light');
                    btn.classList.add('btn-success');

                    setTimeout(() => {
                        btn.innerHTML = originalHtml;
                        btn.classList.remove('btn-success');
                        btn.classList.add('btn-outline-light');
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制代码');
                });
            }
        }

        // API测试功能
        async function testApiCall() {
            {% if user_info.api_key_info %}
            const apiKey = '{{ user_info.api_key_info.key }}';
            const baseUrl = document.getElementById('apiEndpoint').value.replace('/api/v1/generate_image', '');

            const prompt = document.getElementById('testPrompt').value;
            const negativePrompt = document.getElementById('testNegativePrompt').value;
            const width = parseInt(document.getElementById('testWidth').value);
            const height = parseInt(document.getElementById('testHeight').value);
            const steps = parseInt(document.getElementById('testSteps').value);
            const cfg = parseFloat(document.getElementById('testCfg').value);

            const testBtn = document.getElementById('testApiBtn');
            const testResult = document.getElementById('testResult');

            // 禁用按钮并显示加载状态
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>测试中...';
            testResult.style.display = 'block';
            testResult.innerHTML = '<div class="alert alert-info"><i class="fas fa-clock me-2"></i>正在调用API，请稍候...</div>';

            try {
                const response = await fetch(`${baseUrl}/api/v1/generate_image`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        negative_prompt: negativePrompt,
                        width: width,
                        height: height,
                        steps: steps,
                        cfg: cfg,
                        model_index: 0,
                        seed: -1
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    testResult.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>API调用成功！</h6>
                            <p><strong>消耗积分：</strong>${result.data.points_used}</p>
                            <p><strong>剩余积分：</strong>${result.data.remaining_points}</p>
                            <p><strong>模型：</strong>${result.data.model_name}</p>
                            <p><strong>图像URL：</strong></p>
                            <div class="input-group">
                                <input type="text" class="form-control font-monospace" value="${result.data.image_url}" readonly>
                                <button class="btn btn-outline-secondary" onclick="copyText('${result.data.image_url}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <img src="${result.data.image_url}" class="img-fluid rounded" style="max-width: 300px;" alt="生成的图像">
                            </div>
                        </div>
                    `;
                } else {
                    testResult.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>API调用失败</h6>
                            <p><strong>错误信息：</strong>${result.error || result.message || '未知错误'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                testResult.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>请求失败</h6>
                        <p><strong>错误信息：</strong>${error.message}</p>
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-play me-1"></i>测试API调用';
            }
            {% else %}
            alert('请先生成API密钥');
            {% endif %}
        }

        // 生成测试代码
        function generateTestCode() {
            const prompt = document.getElementById('testPrompt').value;
            const negativePrompt = document.getElementById('testNegativePrompt').value;
            const width = parseInt(document.getElementById('testWidth').value);
            const height = parseInt(document.getElementById('testHeight').value);
            const steps = parseInt(document.getElementById('testSteps').value);
            const cfg = parseFloat(document.getElementById('testCfg').value);

            const baseUrl = document.getElementById('apiEndpoint').value.replace('/api/v1/generate_image', '');

            const code = `curl -X POST "${baseUrl}/api/v1/generate_image" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "prompt": "${prompt.replace(/"/g, '\\"')}",
    "negative_prompt": "${negativePrompt.replace(/"/g, '\\"')}",
    "width": ${width},
    "height": ${height},
    "steps": ${steps},
    "cfg": ${cfg},
    "model_index": 0,
    "seed": -1
  }'`;

            navigator.clipboard.writeText(code).then(() => {
                alert('测试代码已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制代码');
            });
        }

        // 复制文本到剪贴板
        function copyText(text) {
            navigator.clipboard.writeText(text).then(() => {
                const btn = event.target.closest('button');
                const originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.classList.remove('btn-outline-secondary');
                btn.classList.add('btn-success');

                setTimeout(() => {
                    btn.innerHTML = originalHtml;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-secondary');
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // API密钥管理函数
        function generateApiKey() {
            if (confirm('确定要生成新的API密钥吗？')) {
                fetch('/api/generate_api_key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('API密钥生成成功！\n\n密钥: ' + data.api_key + '\n\n请妥善保管，刷新页面后将无法再次查看完整密钥。');
                        location.reload();
                    } else {
                        alert('生成失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('生成API密钥时发生错误');
                });
            }
        }

        function regenerateApiKey() {
            if (confirm('确定要重新生成API密钥吗？旧密钥将立即失效！')) {
                generateApiKey();
            }
        }

        function deleteApiKey() {
            if (confirm('确定要删除API密钥吗？此操作不可撤销！')) {
                fetch('/api/delete_api_key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('API密钥已删除');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除API密钥时发生错误');
                });
            }
        }

        function showApiKey() {
            {% if user_info.api_key_info %}
            const apiKey = '{{ user_info.api_key_info.key }}';
            const maskedKey = apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 8);

            if (confirm('确定要查看完整的API密钥吗？\n\n部分密钥: ' + maskedKey)) {
                // 创建一个临时的文本区域来复制密钥
                const textArea = document.createElement('textarea');
                textArea.value = apiKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                alert('API密钥已复制到剪贴板！\n\n' + apiKey);
            }
            {% endif %}
        }
    </script>
</body>
</html>
